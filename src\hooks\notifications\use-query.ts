"use client";

import { useRef } from "react";
import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";
import revalidateTag from "@/utils/revalidate-tag";

// Toast configurations
const success = {
  style: {
    background: "#10B981",
    color: "#FFFFFF",
    border: "none",
  },
};

const failed = {
  style: {
    background: "#EF4444",
    color: "#FFFFFF",
    border: "none",
  },
};

/**
 * Deep comparison function to check if two objects are equal
 * @param {any} obj1 - First object to compare
 * @param {any} obj2 - Second object to compare
 * @return {boolean} True if objects are deeply equal, false otherwise
 */
function deepEqual(obj1: any, obj2: any): boolean {
  if (obj1 === obj2) return true;

  if (obj1 == null || obj2 == null) return false;

  if (typeof obj1 !== typeof obj2) return false;

  if (typeof obj1 !== "object") return obj1 === obj2;

  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);

  if (keys1.length !== keys2.length) return false;

  for (const key of keys1) {
    if (!keys2.includes(key)) return false;
    if (!deepEqual(obj1[key], obj2[key])) return false;
  }

  return true;
}

/**
 * Custom hook for updating notification settings with optimized API handling
 * Features:
 * - Change detection to prevent unnecessary API calls
 * - Request deduplication to prevent multiple concurrent requests
 * - Proper loading state management
 * - User-friendly error handling
 * - Follows Applications component patterns for consistency
 * @return {Object} React Query mutation object with optimized API calls
 */
export const useUpdateNotificationSettings = () => {
  const lastDataRef = useRef<INotificationPreferences | null>(null);
  const currentRequestRef = useRef<AbortController | null>(null);

  return useMutation({
    mutationFn: async (data: INotificationPreferences) => {
      // Change detection - only proceed if data has actually changed
      if (lastDataRef.current && deepEqual(lastDataRef.current, data)) {
        // No changes detected, skip API call to prevent unnecessary requests
        return { message: "No changes to save" };
      }

      // Cancel any existing request
      if (currentRequestRef.current) {
        currentRequestRef.current.abort();
      }

      // Create new abort controller for this request
      const abortController = new AbortController();
      currentRequestRef.current = abortController;

      try {
        // Use Next.js API route following Applications pattern
        const response = await fetch("/api/notifications/settings", {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(data),
          cache: "no-store",
          signal: abortController.signal,
        });

        // Check if request was aborted
        if (abortController.signal.aborted) {
          throw new Error("Request was cancelled");
        }

        if (!response.ok) {
          const error = await response.json();
          throw new Error(
            error.message || "Failed to update notification settings"
          );
        }

        const result = await response.json();

        // Update last data reference on successful save
        lastDataRef.current = { ...data };

        return result;
      } finally {
        // Clear current request reference
        if (currentRequestRef.current === abortController) {
          currentRequestRef.current = null;
        }
      }
    },
    onSuccess: (data) => {
      // Only show success toast if there were actual changes
      if (data?.message !== "No changes to save") {
        toast.success("Notification settings updated successfully", {
          description: "Your notification preferences have been saved",
          ...success,
        });
      }
      revalidateTag(["notification-settings"]);
    },
    onError: (error: any) => {
      // Don't show error for cancelled requests
      if (error.message === "Request was cancelled") {
        return;
      }

      // Handle different types of errors with user-friendly messages
      let errorMessage = "Failed to update notification settings";
      let errorDescription = "Please try again later";

      if (error.name === "AbortError") {
        errorMessage = "Request timed out";
        errorDescription =
          "The request took too long. Please check your connection and try again.";
      } else if (error.message) {
        if (
          error.message.includes("network") ||
          error.message.includes("fetch")
        ) {
          errorMessage = "Network error";
          errorDescription =
            "Please check your internet connection and try again.";
        } else if (
          error.message.includes("unauthorized") ||
          error.message.includes("401")
        ) {
          errorMessage = "Session expired";
          errorDescription = "Please refresh the page and log in again.";
        } else {
          errorMessage = error.message;
        }
      }

      toast.error(errorMessage, {
        description: errorDescription,
        ...failed,
      });
    },
    // Simplified retry configuration
    retry: (failureCount, error) => {
      // Don't retry on client errors (4xx) or cancelled requests
      if (
        error.message === "Request was cancelled" ||
        error.name === "AbortError" ||
        (error.status >= 400 && error.status < 500)
      ) {
        return false;
      }
      // Retry up to 1 time for server errors
      return failureCount < 1;
    },
    retryDelay: 1000, // Simple 1 second delay
  });
};
